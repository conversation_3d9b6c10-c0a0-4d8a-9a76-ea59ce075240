//
//  ZenTomatoApp.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/13.
//  主应用入口 - 菜单栏应用
//

import SwiftUI
import AppKit

@main
struct ZenTomatoApp: App {
    // 使用 NSApplicationDelegateAdaptor 来管理应用生命周期
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    var body: some Scene {
        // 创建一个空的场景，因为我们使用菜单栏
        Settings {
            EmptyView()
        }
    }
}

// MARK: - App Delegate
class AppDelegate: NSObject, NSApplicationDelegate {
    
    /// 菜单栏管理器
    var menuBarManager: MenuBarManager?
    
    /// 计时引擎
    let timerEngine = TimerEngine()
    
    /// 音频播放器
    let audioPlayer = AudioPlayer()
    
    /// 通知管理器
    let notificationManager = NotificationManager()
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        // 隐藏主窗口和 Dock 图标
        NSApp.setActivationPolicy(.accessory)
        
        // 初始化菜单栏
        menuBarManager = MenuBarManager(
            timerEngine: timerEngine,
            audioPlayer: audioPlayer,
            notificationManager: notificationManager
        )
        menuBarManager?.setupMenuBar()
        
        // 请求通知权限
        notificationManager.requestPermission()
        
        // 设置观察者
        setupObservers()
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        // 清理资源
        timerEngine.stop()
        audioPlayer.stopAllSounds()
    }
    
    /// 设置通知观察者
    private func setupObservers() {
        // 监听计时器阶段开始
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhaseStarted(_:)),
            name: .timerPhaseStarted,
            object: nil
        )

        // 监听计时器阶段完成
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhaseCompleted(_:)),
            name: .timerPhaseCompleted,
            object: nil
        )

        // 监听通知快速操作 - 跳过休息
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleSkipBreakRequested(_:)),
            name: .skipBreakRequested,
            object: nil
        )

        // 监听通知快速操作 - 立即开始工作
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleStartWorkRequested(_:)),
            name: .startWorkRequested,
            object: nil
        )

        // 监听通知点击
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNotificationTapped(_:)),
            name: .notificationTapped,
            object: nil
        )
    }
    
    @objc private func handlePhaseStarted(_ notification: Notification) {
        guard let phase = notification.userInfo?["phase"] as? TimerPhase else { return }
        
        // 播放开始音效
        if phase == .work {
            audioPlayer.playWindupSound()
            audioPlayer.startTickingSound()
        } else {
            audioPlayer.stopTickingSound()
        }
    }
    
    @objc private func handlePhaseCompleted(_ notification: Notification) {
        guard let phase = notification.userInfo?["phase"] as? TimerPhase else { return }
        
        // 播放结束音效
        audioPlayer.playDingSound()
        audioPlayer.stopTickingSound()
        
        // 发送通知
        switch phase {
        case .work:
            let nextPhase = timerEngine.completedCycles % timerEngine.configuration.cyclesBeforeLongBreak == 0 
                ? TimerPhase.longBreak 
                : TimerPhase.shortBreak
            notificationManager.sendBreakStartNotification(
                duration: nextPhase == .longBreak 
                    ? timerEngine.configuration.longBreakDuration 
                    : timerEngine.configuration.shortBreakDuration
            )
        case .shortBreak, .longBreak:
            notificationManager.sendBreakEndNotification()
        }
    }

    /// 处理跳过休息请求
    @objc private func handleSkipBreakRequested(_ notification: Notification) {
        // 检查当前状态是否允许跳过休息
        guard (timerEngine.currentPhase == .shortBreak || timerEngine.currentPhase == .longBreak) else {
            print("Cannot skip break: not in break phase (current: \(timerEngine.currentPhase))")
            return
        }

        guard (timerEngine.currentState == .running || timerEngine.currentState == .paused) else {
            print("Cannot skip break: timer not active (current: \(timerEngine.currentState))")
            return
        }

        // 跳过当前阶段
        timerEngine.skip()
        print("Break skipped via notification action")
    }

    /// 处理立即开始工作请求
    @objc private func handleStartWorkRequested(_ notification: Notification) {
        // 如果当前是工作阶段且计时器不在运行状态，则开始计时
        if timerEngine.currentPhase == .work {
            if timerEngine.currentState != .running {
                timerEngine.start()
                print("Work started via notification action")
            } else {
                print("Work already running, ignoring start request")
            }
        }
        // 如果当前是休息阶段，先跳过休息再开始工作
        else if (timerEngine.currentPhase == .shortBreak || timerEngine.currentPhase == .longBreak) {
            guard (timerEngine.currentState == .running || timerEngine.currentState == .paused) else {
                print("Cannot start work: timer not active during break")
                return
            }

            timerEngine.skip()
            print("Break skipped, starting work via notification action")

            // 延迟一点时间等待阶段切换完成，然后开始工作
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }

                if self.timerEngine.currentPhase == .work && self.timerEngine.currentState != .running {
                    self.timerEngine.start()
                    print("Work started after break skip")
                } else {
                    print("Failed to start work after break skip: phase=\(self.timerEngine.currentPhase), state=\(self.timerEngine.currentState)")
                }
            }
        } else {
            print("Cannot start work: unexpected phase \(timerEngine.currentPhase)")
        }
    }

    /// 处理通知点击
    @objc private func handleNotificationTapped(_ notification: Notification) {
        // 显示主界面（如果有菜单栏管理器的话）
        if let menuBarManager = menuBarManager {
            menuBarManager.showPopover()
            print("Popover shown via notification tap")
        } else {
            print("MenuBarManager not available, cannot show popover")
        }
    }
}

// MARK: - Preview Helpers
struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        Text("ZenTomato - 菜单栏应用")
            .frame(width: 300, height: 200)
    }
}
