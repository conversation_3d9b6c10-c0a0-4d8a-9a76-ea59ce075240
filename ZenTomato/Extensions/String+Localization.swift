//
//  String+Localization.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/15.
//  字符串本地化扩展
//

import Foundation

extension String {
    /// 获取本地化字符串
    var localized: String {
        return NSLocalizedString(self, comment: "")
    }
    
    /// 获取带参数的本地化字符串
    func localized(with arguments: CVarArg...) -> String {
        return String(format: NSLocalizedString(self, comment: ""), arguments: arguments)
    }
}

/// 本地化管理器
class LocalizationManager {
    /// 单例实例
    static let shared = LocalizationManager()
    
    private init() {}
    
    /// 获取本地化字符串
    func localizedString(for key: String, arguments: CVarArg...) -> String {
        let format = NSLocalizedString(key, comment: "")
        if arguments.isEmpty {
            return format
        } else {
            return String(format: format, arguments: arguments)
        }
    }
    
    /// 获取通知标题
    func notificationTitle(for type: NotificationType) -> String {
        switch type {
        case .breakStart:
            return "notification.break.start.title".localized
        case .breakEnd:
            return "notification.break.end.title".localized
        case .workStart:
            return "notification.work.start.title".localized
        }
    }
    
    /// 获取通知内容
    func notificationBody(for type: NotificationType, duration: Int? = nil) -> String {
        switch type {
        case .breakStart:
            if let duration = duration {
                return "notification.break.start.body".localized(with: duration)
            } else {
                return "notification.break.start.body".localized
            }
        case .breakEnd:
            return "notification.break.end.body".localized
        case .workStart:
            return "notification.work.start.body".localized
        }
    }
    
    /// 获取通知动作标题
    func notificationActionTitle(for action: NotificationActionType) -> String {
        switch action {
        case .skip:
            return "notification.action.skip".localized
        case .start:
            return "notification.action.start".localized
        }
    }
}

/// 通知类型枚举
enum NotificationType {
    case breakStart
    case breakEnd
    case workStart
}

/// 通知动作类型枚举
enum NotificationActionType {
    case skip
    case start
}
