/* 
   Localizable.strings (English)
   ZenTomato
   
   Created by Ban on 2025/8/15.
   English localization strings file
*/

// MARK: - Notifications
"notification.break.start.title" = "Time for a Break!";
"notification.break.start.body" = "Take a %d-minute break to rest your eyes and body";
"notification.break.end.title" = "Break Over!";
"notification.break.end.body" = "Ready to get back to focused work?";
"notification.work.start.title" = "Start Working!";
"notification.work.start.body" = "Stay focused and give it your all";

// MARK: - Notification Actions
"notification.action.skip" = "Skip Break";
"notification.action.start" = "Start Now";

// MARK: - Interface Text
"timer.status.idle" = "Ready";
"timer.status.running" = "Focusing";
"timer.status.paused" = "Paused";
"timer.status.completed" = "Completed";

"phase.work" = "Focus Work";
"phase.shortBreak" = "Short Break";
"phase.longBreak" = "Long Break";

// MARK: - Settings Interface
"settings.notification.title" = "Notification Settings";
"settings.notification.permission.title" = "Notification Permission";
"settings.notification.permission.notDetermined" = "Not Determined";
"settings.notification.permission.denied" = "Denied";
"settings.notification.permission.authorized" = "Authorized";
"settings.notification.permission.provisional" = "Provisional";
"settings.notification.permission.ephemeral" = "Ephemeral";
"settings.notification.permission.unknown" = "Unknown";

"settings.notification.request" = "Request Permission";
"settings.notification.openSettings" = "Open Settings";
"settings.notification.enabled.message" = "Notifications enabled. You'll be reminded when phases change";

// MARK: - Error Messages
"error.notification.unauthorized" = "Notifications not authorized, cannot send notification";
"error.notification.send.failed" = "Failed to send notification";
"error.notification.permission.failed" = "Notification permission request failed";
