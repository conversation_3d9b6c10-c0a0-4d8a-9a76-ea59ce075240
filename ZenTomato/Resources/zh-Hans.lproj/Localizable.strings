/* 
   Localizable.strings (Chinese Simplified)
   ZenTomato
   
   Created by Ban on 2025/8/15.
   中文本地化字符串文件
*/

// MARK: - 通知相关
"notification.break.start.title" = "休息时间到了！";
"notification.break.start.body" = "休息 %d 分钟，让眼睛和身体放松一下";
"notification.break.end.title" = "休息结束！";
"notification.break.end.body" = "准备好继续专注工作了吗？";
"notification.work.start.title" = "开始工作！";
"notification.work.start.body" = "保持专注，全力以赴";

// MARK: - 通知动作按钮
"notification.action.skip" = "跳过休息";
"notification.action.start" = "立即开始";

// MARK: - 界面文字
"timer.status.idle" = "准备就绪";
"timer.status.running" = "专注中";
"timer.status.paused" = "已暂停";
"timer.status.completed" = "已完成";

"phase.work" = "专注工作";
"phase.shortBreak" = "短暂休息";
"phase.longBreak" = "长休息";

// MARK: - 设置界面
"settings.notification.title" = "通知设置";
"settings.notification.permission.title" = "通知权限";
"settings.notification.permission.notDetermined" = "未决定";
"settings.notification.permission.denied" = "已拒绝";
"settings.notification.permission.authorized" = "已授权";
"settings.notification.permission.provisional" = "临时授权";
"settings.notification.permission.ephemeral" = "临时授权";
"settings.notification.permission.unknown" = "未知";

"settings.notification.request" = "请求权限";
"settings.notification.openSettings" = "打开设置";
"settings.notification.enabled.message" = "通知已启用，将在阶段切换时提醒您";

// MARK: - 错误消息
"error.notification.unauthorized" = "通知未授权，无法发送通知";
"error.notification.send.failed" = "发送通知失败";
"error.notification.permission.failed" = "通知权限请求失败";
