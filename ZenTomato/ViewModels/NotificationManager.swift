//
//  NotificationManager.swift
//  ZenTomato
//
//  Created by Ban on 2025/8/13.
//  通知管理器 - 处理系统通知的发送和权限管理
//

import Foundation
import UserNotifications
import SwiftUI

/// 通知管理器
class NotificationManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    /// 是否已授权通知权限
    @Published var isAuthorized: Bool = false
    
    /// 权限状态
    @Published var authorizationStatus: UNAuthorizationStatus = .notDetermined
    
    // MARK: - Private Properties
    
    /// 通知中心
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // MARK: - Constants
    
    /// 通知标识符
    private enum NotificationIdentifier {
        static let breakStart = "zen.tomato.break.start"
        static let breakEnd = "zen.tomato.break.end"
        static let workStart = "zen.tomato.work.start"
    }
    
    /// 通知动作标识符
    private enum NotificationAction {
        static let skip = "zen.tomato.action.skip"
        static let startNow = "zen.tomato.action.start"
    }
    
    /// 通知类别标识符
    private enum NotificationCategory {
        static let timerAlert = "zen.tomato.category.timer"
    }
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        notificationCenter.delegate = self
        checkAuthorizationStatus()
        setupNotificationCategories()

        // 监听应用进入前台事件，重新检查权限状态
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: NSApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    /// 应用进入前台时重新检查权限状态
    @objc private func applicationDidBecomeActive() {
        checkAuthorizationStatus()
    }
    
    // MARK: - Public Methods
    
    /// 请求通知权限
    func requestPermission() {
        // 检查当前权限状态
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                // 如果权限未决定，则请求权限
                if settings.authorizationStatus == .notDetermined {
                    self?.performPermissionRequest()
                } else {
                    // 权限已经决定，更新状态
                    self?.isAuthorized = settings.authorizationStatus == .authorized
                    self?.authorizationStatus = settings.authorizationStatus
                }
            }
        }
    }

    /// 执行权限请求
    private func performPermissionRequest() {
        notificationCenter.requestAuthorization(options: [.alert, .sound, .badge]) { [weak self] granted, error in
            DispatchQueue.main.async {
                self?.isAuthorized = granted
                self?.checkAuthorizationStatus()

                if let error = error {
                    print(NSLocalizedString("error.notification.permission.failed", comment: "") + ": \(error)")
                } else {
                    // 权限请求完成后，重新设置通知类别（因为权限状态可能已改变）
                    self?.setupNotificationCategories()
                }
            }
        }
    }
    
    /// 发送休息开始通知
    func sendBreakStartNotification(duration: TimeInterval) {
        // 验证时长参数
        guard duration > 0 else {
            print("Invalid duration for break notification: \(duration)")
            return
        }

        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.break.start.title", comment: "")

        // 确保时长显示至少为1分钟
        let minutes = max(1, Int(duration / 60))
        content.body = String(format: NSLocalizedString("notification.break.start.body", comment: ""), minutes)
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "breakStart", "duration": duration]

        // 添加动作按钮
        content.interruptionLevel = .timeSensitive

        sendNotification(content: content, identifier: NotificationIdentifier.breakStart)
    }
    
    /// 发送休息结束通知
    func sendBreakEndNotification() {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.break.end.title", comment: "")
        content.body = NSLocalizedString("notification.break.end.body", comment: "")
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "breakEnd"]

        // 添加动作按钮
        content.interruptionLevel = .timeSensitive

        sendNotification(content: content, identifier: NotificationIdentifier.breakEnd)
    }
    
    /// 发送工作开始通知
    func sendWorkStartNotification() {
        let content = UNMutableNotificationContent()
        content.title = NSLocalizedString("notification.work.start.title", comment: "")
        content.body = NSLocalizedString("notification.work.start.body", comment: "")
        content.sound = .default
        content.categoryIdentifier = NotificationCategory.timerAlert
        content.userInfo = ["type": "workStart"]

        sendNotification(content: content, identifier: NotificationIdentifier.workStart)
    }
    
    /// 移除所有待发送的通知
    func removeAllPendingNotifications() {
        notificationCenter.removeAllPendingNotificationRequests()
    }
    
    /// 移除所有已发送的通知
    func removeAllDeliveredNotifications() {
        notificationCenter.removeAllDeliveredNotifications()
    }
    
    /// 打开系统设置
    func openSystemSettings() {
        // 尝试打开系统设置的通知面板
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.notifications") {
            NSWorkspace.shared.open(url)
        } else {
            // 如果无法打开特定面板，则打开系统设置主界面
            NSWorkspace.shared.open(URL(fileURLWithPath: "/System/Applications/System Preferences.app"))
        }
    }
    
    // MARK: - Private Methods
    
    /// 检查授权状态
    private func checkAuthorizationStatus() {
        notificationCenter.getNotificationSettings { [weak self] settings in
            DispatchQueue.main.async {
                self?.authorizationStatus = settings.authorizationStatus
                self?.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    /// 设置通知类别和动作
    private func setupNotificationCategories() {
        // 创建跳过动作
        let skipAction = UNNotificationAction(
            identifier: NotificationAction.skip,
            title: NSLocalizedString("notification.action.skip", comment: ""),
            options: []
        )

        // 创建立即开始动作
        let startAction = UNNotificationAction(
            identifier: NotificationAction.startNow,
            title: NSLocalizedString("notification.action.start", comment: ""),
            options: [.foreground]
        )

        // 创建计时器提醒类别
        let timerCategory = UNNotificationCategory(
            identifier: NotificationCategory.timerAlert,
            actions: [startAction, skipAction],
            intentIdentifiers: [],
            options: [.customDismissAction]
        )

        // 注册类别
        notificationCenter.setNotificationCategories([timerCategory])
    }
    
    /// 发送通知
    private func sendNotification(content: UNMutableNotificationContent, identifier: String, retryCount: Int = 0) {
        // 检查权限状态
        guard isAuthorized else {
            print(NSLocalizedString("error.notification.unauthorized", comment: ""))
            // 如果权限被拒绝，尝试重新检查权限状态
            checkAuthorizationStatus()
            return
        }

        // 移除之前的同类型通知，避免重复
        notificationCenter.removePendingNotificationRequests(withIdentifiers: [identifier])

        // 创建触发器（立即发送）
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 0.1, repeats: false)

        // 创建请求
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: trigger
        )

        // 添加通知请求
        notificationCenter.add(request) { [weak self] error in
            if let error = error {
                print(NSLocalizedString("error.notification.send.failed", comment: "") + ": \(error)")

                // 如果发送失败且重试次数少于3次，则进行重试
                if retryCount < 3 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        self?.sendNotification(content: content, identifier: identifier, retryCount: retryCount + 1)
                    }
                }
            }
        }
    }
}

// MARK: - UNUserNotificationCenterDelegate

extension NotificationManager: UNUserNotificationCenterDelegate {
    
    /// 处理前台通知展示
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // 即使应用在前台也显示通知
        completionHandler([.banner, .sound, .badge])
    }
    
    /// 处理通知响应
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        let userInfo = response.notification.request.content.userInfo
        
        switch response.actionIdentifier {
        case NotificationAction.skip:
            // 发送跳过休息的通知
            NotificationCenter.default.post(
                name: .skipBreakRequested,
                object: nil,
                userInfo: userInfo
            )
            
        case NotificationAction.startNow:
            // 发送立即开始的通知
            NotificationCenter.default.post(
                name: .startWorkRequested,
                object: nil,
                userInfo: userInfo
            )
            
        case UNNotificationDefaultActionIdentifier:
            // 用户点击了通知本身
            NotificationCenter.default.post(
                name: .notificationTapped,
                object: nil,
                userInfo: userInfo
            )
            
        default:
            break
        }
        
        completionHandler()
    }
}

// MARK: - Notification Names Extension

extension Notification.Name {
    static let skipBreakRequested = Notification.Name("ZenTomato.skipBreakRequested")
    static let startWorkRequested = Notification.Name("ZenTomato.startWorkRequested")
    static let notificationTapped = Notification.Name("ZenTomato.notificationTapped")
}

// MARK: - Preview Helper

extension NotificationManager {
    /// 创建预览用的通知管理器
    static var preview: NotificationManager {
        return NotificationManager()
    }
}

// MARK: - 权限状态显示扩展

extension UNAuthorizationStatus {
    var displayName: String {
        switch self {
        case .notDetermined:
            return NSLocalizedString("settings.notification.permission.notDetermined", comment: "")
        case .denied:
            return NSLocalizedString("settings.notification.permission.denied", comment: "")
        case .authorized:
            return NSLocalizedString("settings.notification.permission.authorized", comment: "")
        case .provisional:
            return NSLocalizedString("settings.notification.permission.provisional", comment: "")
        @unknown default:
            return NSLocalizedString("settings.notification.permission.unknown", comment: "")
        }
    }
    
    var icon: String {
        switch self {
        case .notDetermined:
            return "questionmark.circle"
        case .denied:
            return "xmark.circle"
        case .authorized:
            return "checkmark.circle"
        case .provisional, .ephemeral:
            return "clock.circle"
        @unknown default:
            return "exclamationmark.circle"
        }
    }
    
    var color: Color {
        switch self {
        case .notDetermined:
            return .gray
        case .denied:
            return .red
        case .authorized:
            return .green
        case .provisional, .ephemeral:
            return .orange
        @unknown default:
            return .gray
        }
    }
}