//
//  NotificationManagerTests.swift
//  ZenTomatoTests
//
//  Created by Ban on 2025/8/15.
//  通知管理器测试用例
//

import Testing
import UserNotifications
@testable import ZenTomato

/// 通知管理器测试
struct NotificationManagerTests {
    
    /// 测试通知管理器初始化
    @Test func testNotificationManagerInitialization() async throws {
        let notificationManager = NotificationManager()
        
        // 验证初始状态
        #expect(notificationManager.authorizationStatus == .notDetermined)
        #expect(notificationManager.isAuthorized == false)
    }
    
    /// 测试权限状态显示名称本地化
    @Test func testAuthorizationStatusDisplayNames() async throws {
        // 测试各种权限状态的显示名称
        let statuses: [UNAuthorizationStatus] = [
            .notDetermined,
            .denied,
            .authorized,
            .provisional
        ]
        
        for status in statuses {
            let displayName = status.displayName
            #expect(!displayName.isEmpty, "Display name should not be empty for status: \(status)")
        }
    }
    
    /// 测试权限状态图标
    @Test func testAuthorizationStatusIcons() async throws {
        let statuses: [UNAuthorizationStatus] = [
            .notDetermined,
            .denied,
            .authorized,
            .provisional
        ]
        
        for status in statuses {
            let icon = status.icon
            #expect(!icon.isEmpty, "Icon should not be empty for status: \(status)")
        }
    }
    
    /// 测试通知内容本地化
    @Test func testNotificationLocalization() async throws {
        // 测试通知标题和内容的本地化
        let breakStartTitle = NSLocalizedString("notification.break.start.title", comment: "")
        let breakEndTitle = NSLocalizedString("notification.break.end.title", comment: "")
        let workStartTitle = NSLocalizedString("notification.work.start.title", comment: "")
        
        #expect(!breakStartTitle.isEmpty, "Break start title should not be empty")
        #expect(!breakEndTitle.isEmpty, "Break end title should not be empty")
        #expect(!workStartTitle.isEmpty, "Work start title should not be empty")
        
        // 测试动作按钮本地化
        let skipAction = NSLocalizedString("notification.action.skip", comment: "")
        let startAction = NSLocalizedString("notification.action.start", comment: "")
        
        #expect(!skipAction.isEmpty, "Skip action title should not be empty")
        #expect(!startAction.isEmpty, "Start action title should not be empty")
    }
    
    /// 测试通知发送参数验证
    @Test func testNotificationParameterValidation() async throws {
        let notificationManager = NotificationManager()
        
        // 测试无效的时长参数
        // 注意：这个测试主要验证方法不会崩溃，实际的通知发送需要权限
        notificationManager.sendBreakStartNotification(duration: 0)
        notificationManager.sendBreakStartNotification(duration: -60)
        
        // 测试有效的时长参数
        notificationManager.sendBreakStartNotification(duration: 300) // 5分钟
        notificationManager.sendBreakStartNotification(duration: 900) // 15分钟
    }
    
    /// 测试错误消息本地化
    @Test func testErrorMessageLocalization() async throws {
        let unauthorizedError = NSLocalizedString("error.notification.unauthorized", comment: "")
        let sendFailedError = NSLocalizedString("error.notification.send.failed", comment: "")
        let permissionFailedError = NSLocalizedString("error.notification.permission.failed", comment: "")
        
        #expect(!unauthorizedError.isEmpty, "Unauthorized error message should not be empty")
        #expect(!sendFailedError.isEmpty, "Send failed error message should not be empty")
        #expect(!permissionFailedError.isEmpty, "Permission failed error message should not be empty")
    }
    
    /// 测试设置界面本地化
    @Test func testSettingsLocalization() async throws {
        let notificationTitle = NSLocalizedString("settings.notification.title", comment: "")
        let permissionTitle = NSLocalizedString("settings.notification.permission.title", comment: "")
        let requestButton = NSLocalizedString("settings.notification.request", comment: "")
        let openSettingsButton = NSLocalizedString("settings.notification.openSettings", comment: "")
        let enabledMessage = NSLocalizedString("settings.notification.enabled.message", comment: "")
        
        #expect(!notificationTitle.isEmpty, "Notification title should not be empty")
        #expect(!permissionTitle.isEmpty, "Permission title should not be empty")
        #expect(!requestButton.isEmpty, "Request button text should not be empty")
        #expect(!openSettingsButton.isEmpty, "Open settings button text should not be empty")
        #expect(!enabledMessage.isEmpty, "Enabled message should not be empty")
    }
}

/// 通知快速操作测试
struct NotificationActionTests {
    
    /// 测试通知响应处理
    @Test func testNotificationResponseHandling() async throws {
        // 创建模拟的通知响应
        let userInfo = ["type": "breakStart", "duration": 300.0] as [String: Any]
        
        // 测试跳过休息动作
        let skipNotification = Notification(
            name: .skipBreakRequested,
            object: nil,
            userInfo: userInfo
        )
        
        // 测试立即开始动作
        let startNotification = Notification(
            name: .startWorkRequested,
            object: nil,
            userInfo: userInfo
        )
        
        // 测试通知点击
        let tapNotification = Notification(
            name: .notificationTapped,
            object: nil,
            userInfo: userInfo
        )
        
        // 验证通知名称正确
        #expect(skipNotification.name == .skipBreakRequested)
        #expect(startNotification.name == .startWorkRequested)
        #expect(tapNotification.name == .notificationTapped)
    }
}

/// 本地化测试
struct LocalizationTests {
    
    /// 测试中文本地化
    @Test func testChineseLocalization() async throws {
        // 模拟中文环境
        let _ = Bundle.main
        
        // 测试关键字符串是否存在
        let keys = [
            "notification.break.start.title",
            "notification.break.end.title",
            "notification.work.start.title",
            "notification.action.skip",
            "notification.action.start"
        ]
        
        for key in keys {
            let localizedString = NSLocalizedString(key, comment: "")
            #expect(!localizedString.isEmpty, "Localized string for key '\(key)' should not be empty")
            #expect(localizedString != key, "Localized string for key '\(key)' should be different from key")
        }
    }
}
